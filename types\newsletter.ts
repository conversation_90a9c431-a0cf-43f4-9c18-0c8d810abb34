export interface NewsletterLanguage {
  id: string
  language: string
  language_display: string
  name: string
  updated_in_salesforce: string | null
  created_in_salesforce: boolean
  salesforce_id: string | null
}

export interface NewsletterUser {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface Newsletter {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  created_by?: NewsletterUser
  updated_by?: NewsletterUser
  created_at?: string
  updated_at?: string
}

export interface NewsletterResponse {
  count: number
  next: string | null
  previous: string | null
  results: Newsletter[]
}

export interface NewsletterTableItem {
  id: string
  header: string
  type: string
  status: string
  target: string
  limit: string
  reviewer: string
}
